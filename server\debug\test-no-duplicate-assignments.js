/**
 * Test No Duplicate Assignments
 * 
 * Verify that the new workflow eliminates duplicate assignment creation:
 * 1. Exception trigger should NOT create pending_approval assignments
 * 2. Admin approval should create a single assigned assignment
 * 3. No duplicate assignments in database
 */

const { getClient } = require('../config/database');

async function testNoDuplicateAssignments() {
  console.log('🧪 Testing No Duplicate Assignments Workflow...\n');
  
  const client = await getClient();
  
  try {
    await client.query('BEGIN');
    
    // 1. Create test data
    console.log('1️⃣ CREATING TEST DATA');
    const testData = await createTestData(client);
    
    // 2. Count assignments before exception
    console.log('\n2️⃣ COUNTING ASSIGNMENTS BEFORE EXCEPTION');
    const beforeCount = await countAssignments(client, testData.truck.id);
    
    // 3. Trigger route deviation exception (should NOT create assignment)
    console.log('\n3️⃣ TRIGGERING ROUTE DEVIATION EXCEPTION');
    const exceptionData = await triggerRouteDeviationException(client, testData);
    
    // 4. Count assignments after exception trigger
    console.log('\n4️⃣ COUNTING ASSIGNMENTS AFTER EXCEPTION TRIGGER');
    const afterExceptionCount = await countAssignments(client, testData.truck.id);
    
    // 5. Approve exception (should create single assignment)
    console.log('\n5️⃣ APPROVING EXCEPTION');
    await approveException(client, exceptionData);
    
    // 6. Count assignments after approval
    console.log('\n6️⃣ COUNTING ASSIGNMENTS AFTER APPROVAL');
    const afterApprovalCount = await countAssignments(client, testData.truck.id);
    
    // 7. Verify no duplicates
    console.log('\n7️⃣ VERIFYING NO DUPLICATES');
    await verifyNoDuplicates(client, testData, beforeCount, afterExceptionCount, afterApprovalCount);
    
    await client.query('ROLLBACK'); // Clean up test data
    
    console.log('\n🎉 No duplicate assignments test completed successfully!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
    throw error;
  } finally {
    client.release();
  }
}

async function createTestData(client) {
  const timestamp = Date.now();
  const shortId = timestamp.toString().slice(-6);
  
  // Create test truck
  const truckResult = await client.query(`
    INSERT INTO dump_trucks (truck_number, license_plate, make, model, status, qr_code_data)
    VALUES ($1, $2, 'Test Make', 'Test Model', 'active', '{}')
    RETURNING *
  `, [`DT-ND-${shortId}`, `TEST-ND-${shortId}`]);
  
  // Create test driver
  const driverResult = await client.query(`
    INSERT INTO drivers (employee_id, full_name, license_number, license_expiry, hire_date, status)
    VALUES ($1, 'No Duplicate Test Driver', $2, '2026-12-31', '2024-01-01', 'active')
    RETURNING *
  `, [`EMP-ND-${shortId}`, `LIC-ND-${shortId}`]);
  
  // Create test locations
  const locationAResult = await client.query(`
    INSERT INTO locations (location_code, name, type, status, coordinates, qr_code_data)
    VALUES ($1, 'Original Loading Site', 'loading', 'active', '0.0,0.0', '{}')
    RETURNING *
  `, [`ND-A-${shortId}`]);
  
  const locationBResult = await client.query(`
    INSERT INTO locations (location_code, name, type, status, coordinates, qr_code_data)
    VALUES ($1, 'Unloading Site', 'unloading', 'active', '0.0,0.0', '{}')
    RETURNING *
  `, [`ND-B-${shortId}`]);
  
  const locationCResult = await client.query(`
    INSERT INTO locations (location_code, name, type, status, coordinates, qr_code_data)
    VALUES ($1, 'Deviation Loading Site', 'loading', 'active', '0.0,0.0', '{}')
    RETURNING *
  `, [`ND-C-${shortId}`]);
  
  // Create original assignment
  const originalAssignmentResult = await client.query(`
    INSERT INTO assignments (
      assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id,
      assigned_date, status, priority, expected_loads_per_day
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    RETURNING *
  `, [
    `ND-ORIGINAL-${shortId}`,
    truckResult.rows[0].id,
    driverResult.rows[0].id,
    locationAResult.rows[0].id,
    locationBResult.rows[0].id,
    new Date().toISOString().split('T')[0],
    'assigned',
    'normal',
    1
  ]);
  
  console.log(`   ✅ Created test data:`);
  console.log(`      Truck: ${truckResult.rows[0].truck_number}`);
  console.log(`      Original Assignment: ${originalAssignmentResult.rows[0].assignment_code}`);
  
  return {
    truck: truckResult.rows[0],
    driver: driverResult.rows[0],
    locationA: locationAResult.rows[0],
    locationB: locationBResult.rows[0],
    locationC: locationCResult.rows[0],
    originalAssignment: originalAssignmentResult.rows[0]
  };
}

async function countAssignments(client, truckId) {
  const result = await client.query(`
    SELECT 
        COUNT(*) as total_count,
        COUNT(CASE WHEN status = 'pending_approval' THEN 1 END) as pending_count,
        COUNT(CASE WHEN status = 'assigned' THEN 1 END) as assigned_count
    FROM assignments
    WHERE truck_id = $1
  `, [truckId]);
  
  const counts = result.rows[0];
  console.log(`   📊 Assignment counts for truck ID ${truckId}:`);
  console.log(`      Total: ${counts.total_count}`);
  console.log(`      Pending Approval: ${counts.pending_count}`);
  console.log(`      Assigned: ${counts.assigned_count}`);
  
  return {
    total: parseInt(counts.total_count),
    pending: parseInt(counts.pending_count),
    assigned: parseInt(counts.assigned_count)
  };
}

async function triggerRouteDeviationException(client, testData) {
  console.log('   📋 Simulating route deviation exception trigger...');
  
  // Simulate the scanner.js route deviation logic (new workflow)
  // This should NOT create any assignments
  
  // Create trip log with exception
  const tripResult = await client.query(`
    INSERT INTO trip_logs (
      assignment_id, trip_number, status, loading_start_time,
      is_exception, exception_reason
    ) VALUES ($1, $2, $3, $4, $5, $6)
    RETURNING *
  `, [
    testData.originalAssignment.id,
    1,
    'exception_pending',
    new Date(),
    true,
    'Route deviation test - no duplicate assignments'
  ]);
  
  // Store proposed assignment data in trip notes (new workflow)
  const proposedAssignmentData = {
    truck_id: testData.originalAssignment.truck_id,
    driver_id: testData.originalAssignment.driver_id,
    loading_location_id: testData.locationC.id, // Deviation location
    unloading_location_id: testData.originalAssignment.unloading_location_id,
    assigned_date: new Date().toISOString().split('T')[0],
    priority: 'normal',
    expected_loads_per_day: 1
  };
  
  await client.query(`
    UPDATE trip_logs
    SET notes = $1
    WHERE id = $2
  `, [
    JSON.stringify({
      proposed_assignment_data: proposedAssignmentData,
      original_assignment_id: testData.originalAssignment.id,
      workflow_version: 'no_pending_assignment',
      actual_location: {
        id: testData.locationC.id,
        name: testData.locationC.name,
        type: testData.locationC.type
      }
    }),
    tripResult.rows[0].id
  ]);
  
  // Create approval record
  const approvalResult = await client.query(`
    INSERT INTO approvals (
      trip_log_id, exception_type, exception_description,
      severity, reported_by, status,
      created_at, updated_at
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    RETURNING *
  `, [
    tripResult.rows[0].id,
    'Route Deviation',
    `Truck ${testData.truck.truck_number} loading at ${testData.locationC.name} instead of assigned ${testData.locationA.name}`,
    'medium',
    1,
    'pending',
    new Date(),
    new Date()
  ]);
  
  console.log(`   ✅ Exception triggered:`);
  console.log(`      Trip ID: ${tripResult.rows[0].id}`);
  console.log(`      Approval ID: ${approvalResult.rows[0].id}`);
  console.log(`      Workflow: no_pending_assignment`);
  
  return {
    trip: tripResult.rows[0],
    approval: approvalResult.rows[0]
  };
}

async function approveException(client, exceptionData) {
  console.log('   📋 Approving exception...');
  
  // Use the exception flow manager to approve the exception
  const { processApprovalAndUpdateTrip } = require('../utils/exception-flow-manager');
  
  const result = await processApprovalAndUpdateTrip(
    client,
    exceptionData.approval.id,
    'approved',
    1, // reviewedBy
    'Test approval - no duplicate assignments'
  );
  
  console.log(`   ✅ Exception approved:`);
  console.log(`      Processing time: ${result.processing_time}ms`);
  console.log(`      Trip status: ${result.trip_update?.new_status}`);
  
  return result;
}

async function verifyNoDuplicates(client, testData, beforeCount, afterExceptionCount, afterApprovalCount) {
  console.log('   📋 Verifying no duplicate assignments...');
  
  // Check 1: No assignments created during exception trigger
  const exceptionTriggerIncrease = afterExceptionCount.total - beforeCount.total;
  console.log(`\n   📊 Assignment changes during exception trigger:`);
  console.log(`      Before: ${beforeCount.total} total`);
  console.log(`      After: ${afterExceptionCount.total} total`);
  console.log(`      Increase: ${exceptionTriggerIncrease} ${exceptionTriggerIncrease === 0 ? '✅' : '❌'}`);
  
  // Check 2: Only one assignment created during approval
  const approvalIncrease = afterApprovalCount.total - afterExceptionCount.total;
  console.log(`\n   📊 Assignment changes during approval:`);
  console.log(`      Before: ${afterExceptionCount.total} total`);
  console.log(`      After: ${afterApprovalCount.total} total`);
  console.log(`      Increase: ${approvalIncrease} ${approvalIncrease === 1 ? '✅' : '❌'}`);
  
  // Check 3: No pending_approval assignments
  console.log(`\n   📊 Pending approval assignments:`);
  console.log(`      After exception: ${afterExceptionCount.pending} ${afterExceptionCount.pending === 0 ? '✅' : '❌'}`);
  console.log(`      After approval: ${afterApprovalCount.pending} ${afterApprovalCount.pending === 0 ? '✅' : '❌'}`);
  
  // Check 4: Verify assignment details
  const assignmentDetailsResult = await client.query(`
    SELECT 
        assignment_code,
        status,
        loading_location_id,
        unloading_location_id,
        created_at
    FROM assignments
    WHERE truck_id = $1
    ORDER BY created_at DESC
  `, [testData.truck.id]);
  
  console.log(`\n   📊 All assignments for truck ${testData.truck.truck_number}:`);
  assignmentDetailsResult.rows.forEach((row, i) => {
    const isDeviation = row.loading_location_id == testData.locationC.id;
    const statusIcon = row.status === 'assigned' ? '✅' : row.status === 'pending_approval' ? '⚠️' : '🔄';
    console.log(`      ${i+1}. ${row.assignment_code}: ${row.status} ${statusIcon} ${isDeviation ? '(deviation)' : '(original)'}`);
  });
  
  // Final verification
  const checks = [
    { name: 'No assignments created during exception trigger', passed: exceptionTriggerIncrease === 0 },
    { name: 'Exactly one assignment created during approval', passed: approvalIncrease === 1 },
    { name: 'No pending_approval assignments after exception', passed: afterExceptionCount.pending === 0 },
    { name: 'No pending_approval assignments after approval', passed: afterApprovalCount.pending === 0 }
  ];
  
  console.log(`\n   🎯 DUPLICATE PREVENTION VERIFICATION:`);
  let passedChecks = 0;
  checks.forEach((check, index) => {
    const icon = check.passed ? '✅' : '❌';
    console.log(`      ${index + 1}. ${check.name}: ${icon}`);
    if (check.passed) passedChecks++;
  });
  
  console.log(`\n   📊 Overall Result: ${passedChecks}/${checks.length} checks passed`);
  
  if (passedChecks === checks.length) {
    console.log('   🎉 NO DUPLICATE ASSIGNMENTS! The new workflow is working correctly!');
  } else {
    console.log('   ⚠️ Some duplicate assignment issues may still exist');
  }
}

// Run the test
if (require.main === module) {
  testNoDuplicateAssignments()
    .then(() => {
      console.log('\n🎯 No duplicate assignments test completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed:', error.message);
      process.exit(1);
    });
}

module.exports = { testNoDuplicateAssignments };
