# Search Functionality Test Plan

## Test Cases for Trip Monitoring Search

### Test 1: Basic Search Functionality
1. Navigate to Trip Monitoring page
2. Type "DT-100" in search field
3. Verify: Results filter to show only trips with DT-100 truck
4. Verify: No "Failed to retrieve trips" error appears
5. Verify: Page resets to 1 automatically

### Test 2: Driver Name Search
1. Type driver name in search field
2. Verify: Results filter to show trips for that driver
3. Verify: Search is case-insensitive

### Test 3: Location Search
1. Type location name in search field
2. Verify: Results filter to show trips with that location
3. Verify: Both loading and unloading locations are searched

### Test 4: Clear Search
1. Clear search field
2. Verify: All trips are shown again
3. Verify: No errors occur

## Test Cases for Truck Trip Summary Search

### Test 1: Truck Number Search
1. Navigate to Truck Trip Summary page
2. Type "DT-101" in search field
3. Verify: Results filter to show only DT-101 entries
4. Verify: Metric cards show real-time data (not hardcoded values)

### Test 2: Driver Search
1. Type driver name in search field
2. Verify: Results filter correctly
3. Verify: Pagination works with search results

### Test 3: Assignment Code Search
1. Type assignment code in search field
2. Verify: Results filter correctly
3. Verify: Search works across all supported fields

### Test 4: Pagination with Search
1. Perform a search that returns multiple pages
2. Navigate between pages
3. Verify: Search filter is maintained across pages
4. Verify: Page numbers update correctly

## Expected Results

### Trip Monitoring
- ✅ No "Failed to retrieve trips" errors
- ✅ Search works across all supported fields
- ✅ Automatic page reset to 1 on new search
- ✅ Real-time filtering without manual API calls

### Truck Trip Summary
- ✅ Search works across truck numbers, drivers, locations
- ✅ Metric cards show real-time data (not 4, 000000000, 01200)
- ✅ Pagination works correctly with search
- ✅ Server-side filtering for better performance

## API Endpoints to Test

### Trip Monitoring
```
GET /api/trips?search=DT-100&page=1&limit=10
```

### Truck Trip Summary
```
GET /api/trucks/trip-summary?search=DT-100&page=1&limit=10
```

Both should return:
- Filtered data based on search term
- Proper pagination information
- No errors or timeouts

## Performance Expectations

- Search response time: < 500ms
- No excessive API calls while typing
- Smooth user experience with immediate feedback
- Proper error handling for network issues
