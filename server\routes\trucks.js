const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const auth = require('../middleware/auth');
const Joi = require('joi');
const QRCode = require('qrcode');

// Validation schemas
const truckSchema = Joi.object({
  truck_number: Joi.string().min(3).max(20).required(),
  license_plate: Joi.string().min(3).max(20).required(),
  make: Joi.string().max(50).optional(),
  model: Joi.string().max(50).optional(),
  year: Joi.number().integer().min(1990).max(new Date().getFullYear() + 1).optional(),
  capacity_tons: Joi.number().positive().max(100).optional(),
  status: Joi.string().valid('active', 'inactive', 'maintenance', 'retired').optional(),
  notes: Joi.string().max(1000).optional().allow('')
});

const updateTruckSchema = truckSchema.fork(['truck_number', 'license_plate'], (schema) => schema.optional());

// @route   GET /api/trucks
// @desc    Get all trucks with filtering and search
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      make = '',
      sortBy = 'truck_number',
      sortOrder = 'asc'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    const validSortColumns = ['truck_number', 'license_plate', 'make', 'model', 'year', 'capacity_tons', 'status', 'created_at'];
    const validSortOrders = ['asc', 'desc'];
    
    const sortColumn = validSortColumns.includes(sortBy) ? sortBy : 'truck_number';
    const sortDirection = validSortOrders.includes(sortOrder.toLowerCase()) ? sortOrder.toUpperCase() : 'ASC';

    let whereConditions = [];
    let queryParams = [];
    let paramCount = 0;

    // Search functionality
    if (search) {
      paramCount++;
      whereConditions.push(`(
        truck_number ILIKE $${paramCount} OR 
        license_plate ILIKE $${paramCount} OR 
        make ILIKE $${paramCount} OR 
        model ILIKE $${paramCount} OR
        notes ILIKE $${paramCount}
      )`);
      queryParams.push(`%${search}%`);
    }

    // Status filter
    if (status) {
      paramCount++;
      whereConditions.push(`status = $${paramCount}`);
      queryParams.push(status);
    }

    // Make filter
    if (make) {
      paramCount++;
      whereConditions.push(`make ILIKE $${paramCount}`);
      queryParams.push(`%${make}%`);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM dump_trucks ${whereClause}`;
    const countResult = await query(countQuery, queryParams);
    const totalItems = parseInt(countResult.rows[0].count);

    // Get paginated data
    const dataQuery = `
      SELECT 
        id, truck_number, license_plate, make, model, year, 
        capacity_tons, status, notes, created_at, updated_at
      FROM dump_trucks 
      ${whereClause}
      ORDER BY ${sortColumn} ${sortDirection}
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;
    
    queryParams.push(parseInt(limit), offset);
    const trucksResult = await query(dataQuery, queryParams);

    const totalPages = Math.ceil(totalItems / parseInt(limit));

    res.json({
      success: true,
      data: trucksResult.rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems,
        itemsPerPage: parseInt(limit),
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });

  } catch (error) {
    console.error('Get trucks error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve trucks'
    });
  }
});

// @route   GET /api/trucks/trip-summary
// @desc    Get trip counts and assignment details for each truck (with filters and CSV export)
// @access  Private (All authenticated users)
router.get('/trip-summary', auth, async (req, res) => {
  try {
    // Allow all authenticated users to view trip summary for operational transparency
    // Admin/supervisor users get additional details, regular users get basic summary
    const {
      status = '',
      driver = '',
      search = '',
      date_from = '',
      date_to = '',
      export_csv = false
    } = req.query;

    // Query to get truck, driver, assignment, and trip counts
    const baseQuery = `
      SELECT
        dt.id as truck_id,
        dt.truck_number,
        dt.license_plate,
        dt.status as truck_status,
        d.id as driver_id,
        d.full_name as driver_name,
        a.id as assignment_id,
        a.assignment_code,
        a.status as assignment_status,
        a.assigned_date,
        ll.name as loading_location,
        ul.name as unloading_location,
        COUNT(tl.id) as total_trips,
        COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_trips,
        COUNT(CASE WHEN tl.status = 'loading_start' THEN 1 END) as loading_trips,
        COUNT(CASE WHEN tl.status = 'unloading_start' THEN 1 END) as unloading_trips,
        COUNT(CASE WHEN tl.status = 'exception_pending' THEN 1 END) as exception_trips,
        MAX(tl.created_at) as last_trip_date,

        -- Dynamic Assignment Adaptation fields (if available)
        a.is_adaptive,
        a.adaptation_strategy,
        a.adaptation_confidence
      FROM dump_trucks dt
      LEFT JOIN assignments a ON dt.id = a.truck_id AND a.status IN ('assigned', 'in_progress')
      LEFT JOIN drivers d ON a.driver_id = d.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN trip_logs tl ON a.id = tl.assignment_id
    `;

    let whereConditions = [];
    let queryParams = [];
    let paramCount = 0;

    // Add search functionality
    if (search) {
      paramCount++;
      whereConditions.push(`(
        dt.truck_number ILIKE $${paramCount} OR
        dt.license_plate ILIKE $${paramCount} OR
        d.full_name ILIKE $${paramCount} OR
        ll.name ILIKE $${paramCount} OR
        ul.name ILIKE $${paramCount} OR
        a.assignment_code ILIKE $${paramCount}
      )`);
      queryParams.push(`%${search}%`);
    }

    // Add filters
    if (status) {
      paramCount++;
      whereConditions.push(`dt.status = $${paramCount}`);
      queryParams.push(status);
    }

    if (driver) {
      paramCount++;
      whereConditions.push(`d.full_name ILIKE $${paramCount}`);
      queryParams.push(`%${driver}%`);
    }

    // Add date filters
    if (date_from) {
      paramCount++;
      whereConditions.push(`a.assigned_date >= $${paramCount}`);
      queryParams.push(date_from);
    }

    if (date_to) {
      paramCount++;
      whereConditions.push(`a.assigned_date <= $${paramCount}`);
      queryParams.push(date_to);
    }

    let whereClause = '';
    if (whereConditions.length > 0) {
      whereClause = 'WHERE ' + whereConditions.join(' AND ');
    }

    const groupByClause = `
      GROUP BY dt.id, dt.truck_number, dt.license_plate, dt.status,
               d.id, d.full_name, a.id, a.assignment_code, a.status, a.assigned_date,
               ll.name, ul.name, a.is_adaptive, a.adaptation_strategy, a.adaptation_confidence
      ORDER BY dt.truck_number, a.assigned_date DESC
    `;

    const fullQuery = baseQuery + whereClause + groupByClause;

    const result = await query(fullQuery, queryParams);

    // Get overall statistics for metric cards (not filtered)
    const statsQuery = `
      SELECT
        COUNT(DISTINCT dt.id) as total_trucks,
        COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_trips,
        COUNT(CASE WHEN tl.status IN ('loading_start', 'unloading_start') THEN 1 END) as in_progress_trips
      FROM dump_trucks dt
      LEFT JOIN assignments a ON dt.id = a.truck_id
      LEFT JOIN trip_logs tl ON a.id = tl.assignment_id
    `;

    const statsResult = await query(statsQuery);
    const stats = statsResult.rows[0] || {
      total_trucks: 0,
      completed_trips: 0,
      in_progress_trips: 0
    };

    // Handle CSV export
    if (export_csv === 'true') {
      const csv = result.rows.map(row => ({
        'Truck Number': row.truck_number,
        'License Plate': row.license_plate,
        'Truck Status': row.truck_status,
        'Driver': row.driver_name || 'Unassigned',
        'Assignment Code': row.assignment_code || 'No Assignment',
        'Assignment Status': row.assignment_status || 'No Assignment',
        'Loading Location': row.loading_location || 'Not Assigned',
        'Unloading Location': row.unloading_location || 'Not Assigned',
        'Total Trips': row.total_trips,
        'Completed Trips': row.completed_trips,
        'Loading Trips': row.loading_trips,
        'Unloading Trips': row.unloading_trips,
        'Exception Trips': row.exception_trips,
        'Last Trip Date': row.last_trip_date ? new Date(row.last_trip_date).toLocaleDateString() : 'Never',
        'Is Adaptive': row.is_adaptive ? 'Yes' : 'No',
        'Adaptation Strategy': row.adaptation_strategy || 'N/A',
        'Adaptation Confidence': row.adaptation_confidence || 'N/A'
      }));

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=truck-trip-summary.csv');

      const csvHeader = Object.keys(csv[0] || {}).join(',') + '\n';
      const csvData = csv.map(row => Object.values(row).join(',')).join('\n');

      return res.send(csvHeader + csvData);
    }

    res.json({
      success: true,
      data: result.rows,
      summary: {
        total_trucks: parseInt(stats.total_trucks) || 0,
        completed_trips: parseInt(stats.completed_trips) || 0,
        in_progress_trips: parseInt(stats.in_progress_trips) || 0,
        // Additional filtered summary for the current view
        filtered_trucks: result.rows.length,
        active_trucks: result.rows.filter(row => row.truck_status === 'active').length,
        assigned_trucks: result.rows.filter(row => row.assignment_status === 'assigned').length,
        adaptive_assignments: result.rows.filter(row => row.is_adaptive).length
      }
    });

  } catch (error) {
    console.error('Trip summary error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to load truck trip summary'
    });
  }
});

// @route   GET /api/trucks/:id
// @desc    Get single truck by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await query(
      'SELECT * FROM dump_trucks WHERE id = $1',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Truck not found'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Get truck error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve truck'
    });
  }
});

// @route   POST /api/trucks
// @desc    Create new truck
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    // Validate input
    const { error } = truckSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    const {
      truck_number,
      license_plate,
      make = '',
      model = '',
      year = null,
      capacity_tons = null,
      status = 'active',
      notes = ''
    } = req.body;

    // Check for duplicate truck number or license plate
    const duplicateCheck = await query(
      'SELECT id FROM dump_trucks WHERE truck_number = $1 OR license_plate = $2',
      [truck_number, license_plate]
    );

    if (duplicateCheck.rows.length > 0) {
      return res.status(400).json({
        error: 'Duplicate Error',
        message: 'Truck number or license plate already exists'
      });
    }

    // Generate QR code data (as object for JSONB storage)
    const qrCodeData = {
      type: 'truck',
      id: truck_number,
      assigned_route: 'A-B',
      timestamp: new Date().toISOString()
    };

    // Insert new truck
    const result = await query(
      `INSERT INTO dump_trucks 
       (truck_number, license_plate, make, model, year, capacity_tons, qr_code_data, status, notes)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
       RETURNING *`,
      [truck_number, license_plate, make, model, year, capacity_tons, qrCodeData, status, notes]
    );

    res.status(201).json({
      success: true,
      message: 'Truck created successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Create truck error:', error);
    
    if (error.code === '23505') { // Unique constraint violation
      return res.status(400).json({
        error: 'Duplicate Error',
        message: 'Truck number or license plate already exists'
      });
    }

    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to create truck'
    });
  }
});

// @route   PUT /api/trucks/:id
// @desc    Update truck
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Validate input
    const { error } = updateTruckSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    // Check if truck exists
    const existingTruck = await query(
      'SELECT * FROM dump_trucks WHERE id = $1',
      [id]
    );

    if (existingTruck.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Truck not found'
      });
    }

    const {
      truck_number,
      license_plate,
      make,
      model,
      year,
      capacity_tons,
      status,
      notes
    } = req.body;

    // Check for duplicates (excluding current truck)
    if (truck_number || license_plate) {
      const duplicateCheck = await query(
        'SELECT id FROM dump_trucks WHERE (truck_number = $1 OR license_plate = $2) AND id != $3',
        [truck_number || existingTruck.rows[0].truck_number, 
         license_plate || existingTruck.rows[0].license_plate, 
         id]
      );

      if (duplicateCheck.rows.length > 0) {
        return res.status(400).json({
          error: 'Duplicate Error',
          message: 'Truck number or license plate already exists'
        });
      }
    }

    // Build update query dynamically
    const updates = [];
    const values = [];
    let paramCount = 0;

    const fields = {
      truck_number,
      license_plate,
      make,
      model,
      year,
      capacity_tons,
      status,
      notes
    };

    Object.entries(fields).forEach(([key, value]) => {
      if (value !== undefined) {
        paramCount++;
        updates.push(`${key} = $${paramCount}`);
        values.push(value);
      }
    });

    if (updates.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'No fields to update'
      });
    }

    // Add updated_at
    paramCount++;
    updates.push(`updated_at = $${paramCount}`);
    values.push(new Date());

    // Add id for WHERE clause
    paramCount++;
    values.push(id);

    const updateQuery = `
      UPDATE dump_trucks 
      SET ${updates.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `;

    const result = await query(updateQuery, values);

    res.json({
      success: true,
      message: 'Truck updated successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Update truck error:', error);
    
    if (error.code === '23505') {
      return res.status(400).json({
        error: 'Duplicate Error',
        message: 'Truck number or license plate already exists'
      });
    }

    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to update truck'
    });
  }
});

// @route   DELETE /api/trucks/:id
// @desc    Delete truck
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if truck exists
    const existingTruck = await query(
      'SELECT * FROM dump_trucks WHERE id = $1',
      [id]
    );

    if (existingTruck.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Truck not found'
      });
    }

    // Check if truck has active assignments
    const activeAssignments = await query(
      'SELECT id FROM assignments WHERE truck_id = $1 AND status IN ($2, $3)',
      [id, 'assigned', 'in_progress']
    );

    if (activeAssignments.rows.length > 0) {
      return res.status(400).json({
        error: 'Conflict',
        message: 'Cannot delete truck with active assignments'
      });
    }

    // Delete truck
    await query('DELETE FROM dump_trucks WHERE id = $1', [id]);

    res.json({
      success: true,
      message: 'Truck deleted successfully'
    });

  } catch (error) {
    console.error('Delete truck error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to delete truck'
    });
  }
});

// @route   GET /api/trucks/:id/qr
// @desc    Generate QR code for truck
// @access  Private
router.get('/:id/qr', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await query(
      'SELECT truck_number, qr_code_data FROM dump_trucks WHERE id = $1',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Truck not found'
      });
    }

    const truck = result.rows[0];

    // Convert JSONB qr_code_data to string for QR code generation
    const qrDataString = typeof truck.qr_code_data === 'string'
      ? truck.qr_code_data
      : JSON.stringify(truck.qr_code_data);

    const qrCodeDataURL = await QRCode.toDataURL(qrDataString, {
      width: 200,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });

    res.json({
      success: true,
      data: {
        truck_number: truck.truck_number,
        qr_code: qrCodeDataURL,
        qr_data: truck.qr_code_data // Already parsed as JSONB
      }
    });

  } catch (error) {
    console.error('Generate QR error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to generate QR code'
    });
  }
});

module.exports = router;